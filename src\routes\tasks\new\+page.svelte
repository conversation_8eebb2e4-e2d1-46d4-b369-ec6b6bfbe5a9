<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';
  import RecurrenceEditor from '$lib/client/RecurrenceEditor.svelte';
  import AppLayout from '$lib/components/AppLayout.svelte';
  import { slide } from 'svelte/transition';

  export let data: PageData;

  let title = '';
  let notes = '';
  let priority = 1;
  let categoryId = '';
  let dueDate = '';
  let dueTime = '';
  let hasDueDate = false;
  let subtasks: string[] = [''];
  let loading = false;
  let error = '';
  let success = '';
  let showCategoryDropdown = false;

  // New recurrence rule state
  let hasRecurrence = false;
  let recurrenceRule: any = null;

  const priorityOptions = [
    { value: 0, label: 'Low', color: '#64748b' },
    { value: 1, label: 'Normal', color: '#059669' },
    { value: 2, label: 'High', color: '#dc2626' }
  ];

  function addSubtask() {
    subtasks = [...subtasks, ''];
  }

  function removeSubtask(index: number) {
    subtasks = subtasks.filter((_, i) => i !== index);
  }

  function handleRecurrenceChange(event: CustomEvent) {
    recurrenceRule = event.detail;
  }

  async function handleSubmit() {
    if (!title.trim()) {
      error = 'Title is required';
      return;
    }

    loading = true;
    error = '';

    try {
      // Combine date and time if both are provided
      let combinedDueDate = null;
      if (hasDueDate && dueDate) {
        if (dueTime) {
          combinedDueDate = new Date(`${dueDate}T${dueTime}`);
        } else {
          combinedDueDate = new Date(`${dueDate}T09:00`); // Default to 9 AM
        }
      }

      // Filter out empty subtasks
      const validSubtasks = subtasks.filter(s => s.trim() !== '');

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          notes: notes.trim() || null,
          priority,
          categoryId: categoryId || null,
          dueDate: combinedDueDate?.toISOString() || null,
          subtasks: validSubtasks,
          recurrenceRule: hasRecurrence ? recurrenceRule : null
        }),
      });

      const result = await response.json();

      if (response.ok) {
        // Show brief success message then redirect
        success = 'Task created successfully!';
        // Small delay to show success message, then redirect
        setTimeout(() => {
          goto('/', { invalidateAll: true });
        }, 500);
      } else {
        error = result.error || 'Failed to create task';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>New Task - Routine Mail</title>
</svelte:head>

<AppLayout user={data.user} title="Create New Task">
  <span slot="subtitle">Add a new task to your routine</span>
  <form on:submit|preventDefault={handleSubmit} class="task-form">
    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    <!-- Essential Information Section -->
    <div class="form-section">
      <div class="section-header">
        <h3 class="section-title">Essential Information</h3>
        <p class="section-subtitle">The basics to get your task started</p>
      </div>

      <div class="form-group">
        <label for="title">Task Title *</label>
        <div class="title-input-row">
          <input
            id="title"
            type="text"
            bind:value={title}
            placeholder="What needs to be done?"
            required
            disabled={loading}
            class="primary-input"
          />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Priority Level</label>
          <div class="priority-selector">
            {#each priorityOptions as option}
              <button
                type="button"
                class="priority-btn"
                class:selected={priority === option.value}
                style="--priority-color: {option.color}"
                on:click={() => priority = option.value}
                disabled={loading}
              >
                <div class="priority-indicator"></div>
                {option.label}
              </button>
            {/each}
          </div>
        </div>
      </div>
      <div class="form-group">
        <div class="due-date-toggle">
          <label class="toggle-label">
            <input
              type="checkbox"
              bind:checked={hasDueDate}
              disabled={loading}
              class="toggle-checkbox"
            />
            <span class="toggle-text">Set Due Date</span>
          </label>
        </div>

        {#if hasDueDate}
          <div class="due-date-inputs" transition:slide>
            <div class="date-input-group">
              <label class="input-label">Date</label>
              <div class="custom-date-input">
                <input
                  type="date"
                  bind:value={dueDate}
                  disabled={loading}
                  class="date-input"
                />
                <div class="input-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </div>
              </div>
            </div>
            <div class="time-input-group">
              <label class="input-label">Time (optional)</label>
              <div class="custom-time-input">
                <input
                  type="time"
                  bind:value={dueTime}
                  disabled={loading}
                  class="time-input"
                  placeholder="09:00"
                />
                <div class="input-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12,6 12,12 16,14"></polyline>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <div class="form-group">
        <label>Subtasks (Optional)</label>
        <div class="subtasks-container">
          {#each subtasks as subtask, index}
            <div class="subtask-row">
              <input
                type="text"
                bind:value={subtasks[index]}
                placeholder="Enter subtask"
                disabled={loading}
              />
              {#if subtasks.length > 1}
                <button
                  type="button"
                  class="remove-subtask-btn"
                  on:click={() => removeSubtask(index)}
                  disabled={loading}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              {/if}
            </div>
          {/each}
          <button
            type="button"
            class="add-subtask-btn"
            on:click={addSubtask}
            disabled={loading}
          >
            + Add Subtask
          </button>
        </div>
      </div>
    </div>

    <!-- Additional Details Section -->
    <div class="form-section collapsible">
      <div class="section-header">
        <h3 class="section-title">Additional Details</h3>
        <p class="section-subtitle">Optional information to organize your task</p>
      </div>

      <div class="form-group">
        <label>Category</label>
        <div class="category-dropdown-container">
          <button
            type="button"
            class="category-dropdown-trigger"
            on:click={() => showCategoryDropdown = !showCategoryDropdown}
            disabled={loading}
          >
            <div class="selected-category">
              {#if categoryId}
                {@const selectedCategory = data.categories.find(c => c.id === categoryId)}
                <div class="category-color" style="background: {selectedCategory?.color};"></div>
                <span>{selectedCategory?.name}</span>
              {:else}
                <div class="category-color" style="background: #e5e7eb;"></div>
                <span>No category</span>
              {/if}
            </div>
            <svg class="dropdown-arrow" class:rotated={showCategoryDropdown} width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m6 8 4 4 4-4"/>
            </svg>
          </button>

          {#if showCategoryDropdown}
            <div class="category-dropdown-menu" transition:slide>
              <button
                type="button"
                class="category-option"
                class:selected={!categoryId}
                on:click={() => { categoryId = ''; showCategoryDropdown = false; }}
              >
                <div class="category-color" style="background: #e5e7eb;"></div>
                <span>No category</span>
              </button>
              {#each data.categories as category}
                <button
                  type="button"
                  class="category-option"
                  class:selected={categoryId === category.id}
                  on:click={() => { categoryId = category.id; showCategoryDropdown = false; }}
                >
                  <div class="category-color" style="background: {category.color};"></div>
                  <span>{category.name}</span>
                </button>
              {/each}
            </div>
          {/if}
        </div>
      </div>

      <div class="form-group">
        <label for="notes">Notes</label>
        <textarea
          id="notes"
          bind:value={notes}
          placeholder="Add any additional details or notes..."
          disabled={loading}
          rows="4"
        ></textarea>
      </div>
    </div>

    <!-- Recurrence Section -->
    <div class="form-section collapsible">
      <div class="section-header">
        <h3 class="section-title">Recurrence</h3>
        <p class="section-subtitle">Make this task repeat automatically</p>
      </div>

      <div class="form-group">
        <div class="recurrence-toggle">
          <label class="toggle-label">
            <input
              type="checkbox"
              bind:checked={hasRecurrence}
              disabled={loading}
              class="toggle-checkbox"
            />
            <span class="toggle-text">Make this task recurring</span>
          </label>
        </div>

        {#if hasRecurrence}
          <div class="recurrence-editor-container" transition:slide>
            <RecurrenceEditor on:change={handleRecurrenceChange} />
          </div>
        {/if}
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <button
        type="button"
        class="btn-secondary"
        on:click={() => goto('/')}
        disabled={loading}
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn-primary"
        disabled={loading || !title.trim()}
      >
        {#if loading}
          <div class="loading-spinner"></div>
          Creating...
        {:else}
          Create Task
        {/if}
      </button>
    </div>
  </form>

</AppLayout>

<style>




  .task-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    padding: 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  .form-section {
    padding: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .form-section:last-of-type {
    border-bottom: none;
  }

  .form-section.collapsible {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  }

  .section-header {
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.125rem;
  }

  .section-subtitle {
    color: #6b7280;
    font-size: 0.75rem;
    margin: 0;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  input, textarea, select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease;
  }

  .title-input-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .title-input-row .primary-input {
    flex: 1;
    border: 2px solid #d1d5db !important;
    font-size: 1rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.75rem;
    border-radius: 10px;
  }

  .primary-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.08);
  }

  .toggle-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .toggle-checkbox {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #3b82f6;
  }

  .toggle-text {
    color: #374151;
  }

  .due-date-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin-top: 0.75rem;
    padding: 1.25rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .date-input-group, .time-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .input-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .custom-date-input, .custom-time-input {
    position: relative;
  }

  .date-input, .time-input {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background: rgba(255, 255, 255, 0.95);
  }

  .input-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    pointer-events: none;
  }

  .priority-selector {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .priority-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #374151;
  }

  .priority-btn:hover {
    border-color: var(--priority-color);
    background: rgba(255, 255, 255, 0.95);
  }

  .priority-btn.selected {
    border-color: var(--priority-color);
    background: var(--priority-color);
    color: white;
  }

  .priority-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--priority-color);
  }

  .priority-btn.selected .priority-indicator {
    background: rgba(255, 255, 255, 0.9);
  }

  .subtasks-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .subtask-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .subtask-row input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
  }

  .remove-subtask-btn {
    padding: 0.5rem;
    border: none;
    background: #fee2e2;
    color: #dc2626;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .remove-subtask-btn:hover {
    background: #fecaca;
  }

  .add-subtask-btn {
    padding: 0.75rem 1rem;
    border: 2px dashed #d1d5db;
    background: transparent;
    color: #6b7280;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .add-subtask-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
  }

  .category-dropdown-container {
    position: relative;
  }

  .category-dropdown-trigger {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.95);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
  }

  .category-dropdown-trigger:hover {
    border-color: #9ca3af;
  }

  .selected-category {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .category-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .dropdown-arrow {
    transition: transform 0.2s ease;
    color: #6b7280;
  }

  .dropdown-arrow.rotated {
    transform: rotate(180deg);
  }

  .category-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 10;
    max-height: 200px;
    overflow-y: auto;
  }

  .category-option {
    width: 100%;
    padding: 0.75rem;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s ease;
    text-align: left;
  }

  .category-option:hover {
    background: #f3f4f6;
  }

  .category-option.selected {
    background: #eff6ff;
    color: #1d4ed8;
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }

  .recurrence-editor-container {
    margin-top: 0.75rem;
    padding: 1.25rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.8));
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .form-actions {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  }

  .btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background: rgba(249, 250, 251, 0.95);
    border-color: #9ca3af;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-message, .success-message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .error-message {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }

  .success-message {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  @media (max-width: 768px) {

    .due-date-inputs {
      grid-template-columns: 1fr;
    }

    .form-row {
      grid-template-columns: 1fr;
    }

    .priority-selector {
      flex-direction: column;
    }

    .form-actions {
      flex-direction: column-reverse;
    }

    .btn-primary, .btn-secondary {
      width: 100%;
      justify-content: center;
    }
  }
</style>
