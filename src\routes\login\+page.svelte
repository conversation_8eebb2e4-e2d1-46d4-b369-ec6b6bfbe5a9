<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { openOfficeFlowLoginPopup, redirectToOfficeFlowLogin } from '$lib/client/oauth2.js';

  let email = '';
  let password = '';
  let loading = false;
  let officeFlowLoading = false;
  let error = '';

  async function handleLogin() {
    if (!email || !password) {
      error = 'Please fill in all fields';
      return;
    }

    loading = true;
    error = '';

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok) {
        goto('/dashboard');
      } else {
        error = data.error || 'Login failed';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  async function handleOfficeFlowLogin() {
    officeFlowLoading = true;
    error = '';

    try {
      // Use direct redirect instead of popup to avoid conflicts
      await redirectToOfficeFlowLogin('/dashboard');
    } catch (redirectErr) {
      error = 'Failed to connect to Office Flow. Please try again.';
      officeFlowLoading = false;
    }
  }
</script>

<svelte:head>
  <title>Login - Routine Mail</title>
</svelte:head>

<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <div class="logo">Routine Mail</div>
      <h1>Welcome Back</h1>
      <p>Sign in to your account</p>
    </div>

    <form on:submit|preventDefault={handleLogin} class="auth-form">
      {#if error}
        <div class="error-message">{error}</div>
      {/if}

      <div class="form-group">
        <label for="email">Email</label>
        <input
          id="email"
          type="email"
          bind:value={email}
          placeholder="Enter your email"
          required
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          id="password"
          type="password"
          bind:value={password}
          placeholder="Enter your password"
          required
          disabled={loading}
        />
        <div class="forgot-password-link">
          <a href="/forgot-password">Forgot your password?</a>
        </div>
      </div>

      <button type="submit" class="auth-btn" disabled={loading || officeFlowLoading}>
        {loading ? 'Signing in...' : 'Sign In'}
      </button>
    </form>

    <div class="auth-divider">
      <span>or</span>
    </div>

    <button
      type="button"
      class="oauth-btn office-flow-btn"
      on:click={handleOfficeFlowLogin}
      disabled={loading || officeFlowLoading}
    >
      <svg class="oauth-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
      </svg>
      {officeFlowLoading ? 'Connecting...' : 'Sign in with Office Flow'}
    </button>

    <div class="auth-footer">
      <p>Don't have an account? <a href="/register">Sign up</a></p>
    </div>
  </div>
</div>

<style>
  :global(body) {
    font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  .auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 3rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .auth-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: #2d3748;
    letter-spacing: -0.025em;
    margin-bottom: 1rem;
  }

  .logo::after {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #4299e1, #63b3ed);
    border-radius: 50%;
    margin-left: 0.5rem;
    vertical-align: middle;
  }

  h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0 0 0.5rem 0;
  }

  .auth-header p {
    color: #718096;
    margin: 0;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  label {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.875rem;
  }

  input {
    padding: 0.875rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: white;
  }

  input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  input:disabled {
    background: #f7fafc;
    cursor: not-allowed;
  }

  .auth-btn {
    padding: 0.875rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .auth-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    text-align: center;
  }

  .auth-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
  }

  .auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e2e8f0;
  }

  .auth-divider span {
    background: rgba(255, 255, 255, 0.95);
    padding: 0 1rem;
    color: #718096;
    font-size: 0.875rem;
  }

  .oauth-btn {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #2d3748;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .oauth-btn:hover:not(:disabled) {
    border-color: #cbd5e0;
    background: #f7fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .oauth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .office-flow-btn {
    border-color: #4299e1;
    color: #2b6cb0;
  }

  .office-flow-btn:hover:not(:disabled) {
    border-color: #3182ce;
    background: #ebf8ff;
  }

  .oauth-icon {
    width: 18px;
    height: 18px;
  }

  .auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
  }

  .auth-footer p {
    color: #718096;
    margin: 0;
  }

  .auth-footer a {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
  }

  .auth-footer a:hover {
    text-decoration: underline;
  }

  .forgot-password-link {
    text-align: right;
    margin-top: 0.5rem;
  }

  .forgot-password-link a {
    color: #4299e1;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .forgot-password-link a:hover {
    text-decoration: underline;
  }
</style>
